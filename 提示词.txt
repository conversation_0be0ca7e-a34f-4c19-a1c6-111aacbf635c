请使用MCP工具系统性地完成高考报考智慧指导平台立项文档的重新创建工作，具体要求如下：

**第一步：信息收集与分析**
1. 使用view工具完整读取【项目基本信息.txt】文件内容
2. 使用get_document_text_word-document-server工具逐一读取【资料模版】文件夹下的所有Word文档，包括：
   - 1. 农产品物流订单汇聚系统_立项申请书.docx
   - 2. 农产品物流订单汇聚系统_项目组织机构及人员.docx
   - 3. 农产品物流订单汇聚系统_项目工作任务书.docx
   - 4. 农产品物流订单汇聚系统_立项评审纪要.docx
   - 5.1 农产品物流订单汇聚系统_研究成果报告.docx
   - 5.2 农产品物流订单汇聚系统_效应情况说明.docx
   - 6. 农产品物流订单汇聚系统_项目周报.docx
3. 分析每个模版文档的结构、格式、章节安排和内容框架

**第二步：文档创建与内容替换**
1. 使用create_document_word-document-server为每个文档创建对应的高考报考智慧指导平台版本
2. 严格按照模版的文档结构和格式要求，使用add_heading_word-document-server、add_paragraph_word-document-server、add_table_word-document-server等工具重建文档内容
3. 将所有农产品物流订单汇聚系统相关的内容准确替换为高考报考智慧指导平台的对应内容，包括：
   - 项目名称、目标、背景
   - 技术方案、系统架构
   - 团队成员信息（基于项目基本信息.txt中的人员：孙耿勇、高超、秦义、卫娟、周学杰、王思倩、苏丹、王宇）
   - 时间计划、预算安排
   - 风险分析、预期成果等

**第三步：质量保证与进度汇报**
1. 确保每个文档的章节编号连续、格式统一、内容逻辑清晰
2. 每完成一个文档后，使用send_message_to_user_dingding向"孙耿勇"发送进展汇报，包括：
   - 已完成的文档名称
   - 主要修改内容概述
   - 下一步工作计划
3. 所有文档完成后发送总结汇报

**输出要求：**
- 文档命名格式：高考报考智慧指导平台_[对应模版名称].docx
- 内容必须与项目基本信息.txt中的信息保持一致
- 保持模版的专业性和规范性，同时确保内容的准确性和可信度